using System;
using System.IO;
using System.Net;
using System.Text;
using System.Threading;
using System.Linq;
using UnityEngine;
using UnityEngine.Networking;
using System.Collections;
using Newtonsoft.Json;

/// <summary>
/// API返回结果数据结构
/// </summary>
[System.Serializable]
public class LogUploadCheckResult
{
    public bool Result;
    public string Msg;
}

/// <summary>
/// 日志上传
/// </summary>
public class LogManager : MonoBehaviour
{
    public static LogManager ins;
    public long key;

    private string path;
    private string fileName;

    private string[] files;


    void Awake()
    {
        ins = this;
        key = DateTime.Now.Ticks;

        Application.logMessageReceived += Application_logMessageReceived;
        string tt = DateTime.Now.ToString("yyyy_MM_dd_HHmmss");
        path = Application.streamingAssetsPath + "/logs/";
        if (!System.IO.Directory.Exists(path))
        {
            System.IO.Directory.CreateDirectory(path);
        }
        else
        {
            DealLogFiles();
        }
        UnityEngine.Debug.Log("[LogManager] 日志路径: " + path);

        fileName = string.Format("{0}{1}.txt", path, tt);
    }


    /// <summary>
    /// 删除7天前的日志
    /// </summary>
    private void DealLogFiles()
    {
        string[] allFiles = Directory.GetFiles(path);
        // 过滤掉.meta文件，只保留日志文件
        files = allFiles.Where(file => !file.EndsWith(".meta")).ToArray();

        for (int i = 0; i < files.Length; i++)
        {
            DeleteFile(files[i]);
        }
    }

    public void DeleteFile(string file)
    {
        FileInfo fi = new FileInfo(file);
        var t = DateTime.Now - fi.CreationTime;
        if (t.TotalDays > 7)
        {
            File.Delete(file);
        }
    }

    private void Application_logMessageReceived(string condition, string stackTrace, LogType type)
    {
        string str = System.DateTime.Now.ToString("HH:mm:ss") + condition + System.Environment.NewLine;
        str += stackTrace + System.Environment.NewLine;
        File.AppendAllText(fileName, str);
    }


    public void Start()
    {
        // 启动日志上传检查
        //StartCoroutine(CheckAndUploadLogs());

        StartCoroutine(UploadLogs());
    }

    /// <summary>
    /// 检查是否需要上传日志，如果需要则执行上传
    /// </summary>
    /// <returns></returns>
    private IEnumerator CheckAndUploadLogs()
    {
        // 等待一段时间确保App.Instance已经初始化
        yield return new WaitForSeconds(2f);

        if (string.IsNullOrEmpty(App.Instance.Data.Set.TerminalID))
        {
            Debug.Log("[LogManager] TerminalID为空，无法检查日志上传状态");
            yield break;
        }

        string logUrl = App.Instance.Data.Set.LogUrl;
        if (string.IsNullOrEmpty(logUrl))
        {
            Debug.Log("[LogManager] LogUrl为空，无法检查日志上传状态");
            yield break;
        }

        // 确保URL不以斜杠结尾，避免双斜杠问题
        logUrl = logUrl.TrimEnd('/');

        string checkUrl = string.Format("{0}/getwjuplog.aspx?terminalid={1}&pwd={2}",
            logUrl, App.Instance.Data.Set.TerminalID, App.Instance.Data.Set.Password);

        Debug.Log("[LogManager] 检查日志上传状态，URL: " + checkUrl);

        using (UnityWebRequest request = UnityWebRequest.Get(checkUrl))
        {
            request.timeout = 10;
            yield return request.SendWebRequest();

            if (request.result == UnityWebRequest.Result.Success)
            {
                try
                {
                    string responseText = request.downloadHandler.text;
                    Debug.Log("[LogManager] 检查响应: " + responseText);

                    LogUploadCheckResult result = JsonConvert.DeserializeObject<LogUploadCheckResult>(responseText);

                    if (result != null && result.Result)
                    {
                        Debug.Log("[LogManager] 需要上传日志，开始上传...");
                        StartCoroutine(UploadLogs());
                    }
                    else
                    {
                        Debug.Log("[LogManager] 不需要上传日志");
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError("[LogManager] 解析检查结果失败: " + ex.Message);
                }
            }
            else
            {
                Debug.LogError("[LogManager] 检查日志上传状态失败: " + request.error);
            }
        }
    }

    /// <summary>
    /// 上传日志文件
    /// </summary>
    /// <returns></returns>
    private IEnumerator UploadLogs()
    {
        if (string.IsNullOrEmpty(App.Instance.Data.Set.WJ_Code))
        {
            Debug.Log("[LogManager] WJ_Code为空，无法上传日志");
            yield break;
        }

        if (files == null || files.Length == 0)
        {
            Debug.Log("[LogManager] 没有日志文件需要上传");
            yield break;
        }

        string logUrl = App.Instance.Data.Set.LogUrl;
        if (string.IsNullOrEmpty(logUrl))
        {
            Debug.Log("[LogManager] LogUrl为空，无法上传日志");
            yield break;
        }

        // 确保URL不以斜杠结尾，避免双斜杠问题
        logUrl = logUrl.TrimEnd('/');

        for (int i = 0; i < files.Length; i++)
        {
            string file = files[i];
            // 跳过.meta文件
            if (file.EndsWith(".meta"))
            {
                Debug.Log("[LogManager] 跳过.meta文件: " + file);
                continue;
            }

            if (File.Exists(file))
            {
                Debug.Log("[LogManager] 上传日志文件: " + file);

                string txt = File.ReadAllText(file);
                string fileName = Path.GetFileNameWithoutExtension(file);
                string postUrl = string.Format("{0}/logup?id={1}&file={2}", logUrl, App.Instance.Data.Set.WJ_Code, fileName);

                yield return StartCoroutine(PostLogFile(postUrl, txt));
            }
        }

        Debug.Log("[LogManager] 日志上传完成");
    }

    /// <summary>
    /// 使用UnityWebRequest上传日志文件内容
    /// </summary>
    /// <param name="url">上传URL</param>
    /// <param name="body">日志内容</param>
    /// <returns></returns>
    private IEnumerator PostLogFile(string url, string body)
    {
        byte[] data = Encoding.UTF8.GetBytes(body);

        using (UnityWebRequest request = new UnityWebRequest(url, "POST"))
        {
            request.uploadHandler = new UploadHandlerRaw(data);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "text/html; charset=utf-8");
            request.timeout = 10;

            yield return request.SendWebRequest();

            if (request.result == UnityWebRequest.Result.Success)
            {
                string responseContent = request.downloadHandler.text;
                Debug.Log("[LogManager] 上传成功，响应: " + responseContent);
            }
            else
            {
                Debug.LogError("[LogManager] 上传失败: " + request.error + ", URL: " + url);
            }
        }
    }


}