const express = require('express');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8688;

// 中间件配置
app.use(express.text({ limit: '50mb', type: '*/*' })); // 接受所有类型作为文本
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 日志存储目录
const LOG_DIR = path.join(__dirname, 'logs');

// 确保日志目录存在
if (!fs.existsSync(LOG_DIR)) {
    fs.mkdirSync(LOG_DIR, { recursive: true });
}

// 静态文件服务 - 直接访问日志文件
app.use('/logs', express.static(LOG_DIR, {
    index: false,
    dotfiles: 'ignore'
}));

// 获取设备日志目录
function getDeviceLogDir(deviceId) {
    return path.join(LOG_DIR, `device_${deviceId}`);
}

// 记录访问日志
function logAccess(req, info = '') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${req.method} ${req.url} - ${req.ip} ${info}\n`;
    
    const accessLogFile = path.join(LOG_DIR, 'access.log');
    fs.appendFileSync(accessLogFile, logEntry);
    console.log(logEntry.trim());
}

// API: 检查是否需要上传日志
app.get('/getwjuplog.aspx', (req, res) => {
    const { terminalid, pwd } = req.query;
    
    logAccess(req, `检查上传 - 设备ID: ${terminalid}`);
    
    // 简单验证密码
    if (pwd !== "653033") {
        return res.json({ Result: false, Msg: "密码错误" });
    }
    
    // 允许所有设备上传
    res.json({ Result: true, Msg: "执行成功" });
});

// API: 接收日志上传
app.post('/logup', (req, res) => {
    const { id, file } = req.query;
    // 确保logContent是字符串
    let logContent = req.body;

    // 处理不同类型的请求体
    if (typeof logContent === 'object') {
        // 如果是空对象，可能是express.text()没有正确解析
        if (Object.keys(logContent).length === 0) {
            // 尝试从原始请求中获取数据
            logContent = req.rawBody || '';
        } else {
            logContent = JSON.stringify(logContent);
        }
    } else if (typeof logContent !== 'string') {
        logContent = String(logContent);
    }
    
    logAccess(req, `上传日志 - 设备ID: ${id}, 文件: ${file}`);
    
    if (!id || !file) {
        return res.status(400).send('缺少必要参数');
    }
    
    try {
        // 创建设备日志目录
        const deviceLogDir = getDeviceLogDir(id);
        if (!fs.existsSync(deviceLogDir)) {
            fs.mkdirSync(deviceLogDir, { recursive: true });
        }
        
        // 生成文件名（包含时间戳）
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const fileName = `${file}_${timestamp}.log`;
        const filePath = path.join(deviceLogDir, fileName);
        
        // 保存日志文件
        fs.writeFileSync(filePath, logContent, 'utf8');
        
        res.send('上传成功');
        
    } catch (error) {
        console.error('保存日志文件失败:', error);
        res.status(500).send('保存失败');
    }
});

// Web界面: 主页 - 目录浏览
app.get('/', (req, res) => {
    logAccess(req, '访问主页');
    
    try {
        const devices = fs.readdirSync(LOG_DIR)
            .filter(item => {
                const itemPath = path.join(LOG_DIR, item);
                return fs.statSync(itemPath).isDirectory() && item.startsWith('device_');
            })
            .map(dir => dir.replace('device_', ''));
        
        let html = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>挖机日志管理系统</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
                .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                h1 { color: #333; text-align: center; }
                .device-list { margin-top: 20px; }
                .device-item { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f9f9f9; }
                .device-item h3 { margin-top: 0; color: #555; }
                .btn { display: inline-block; padding: 8px 16px; margin: 5px; text-decoration: none; border-radius: 4px; font-size: 14px; background: #007bff; color: white; }
                .btn:hover { background: #0056b3; }
                .info-section { margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 5px; }
                .api-info { font-family: 'Courier New', monospace; background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 5px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚜 挖机日志管理系统</h1>
                
                <div class="device-list">
        `;
        
        if (devices.length === 0) {
            html += '<p>暂无设备日志</p>';
        } else {
            devices.forEach(deviceId => {
                const deviceLogDir = getDeviceLogDir(deviceId);
                const logFiles = fs.readdirSync(deviceLogDir)
                    .filter(file => file.endsWith('.log'))
                    .length;
                
                html += `
                        <div class="device-item">
                            <h3>设备 ${deviceId}</h3>
                            <p>日志文件数量: ${logFiles}</p>
                            <a href="/browse/${deviceId}" class="btn">浏览日志</a>
                            <a href="/logs/device_${deviceId}" class="btn">直接访问目录</a>
                        </div>
                `;
            });
        }
        
        html += `
                </div>
                
                <div class="info-section">
                    <h3>API接口</h3>
                    <p><strong>检查上传:</strong></p>
                    <div class="api-info">GET /getwjuplog.aspx?terminalid={设备ID}&pwd=653033</div>
                    <p><strong>上传日志:</strong></p>
                    <div class="api-info">POST /logup?id={设备ID}&file={文件名}</div>
                    <p><strong>服务器端口:</strong> ${PORT}</p>
                    <p><strong>日志目录:</strong> <a href="/logs" class="btn">浏览所有日志</a></p>
                </div>
            </div>
        </body>
        </html>
        `;
        
        res.send(html);
        
    } catch (error) {
        res.status(500).send('读取目录失败: ' + error.message);
    }
});

// 设备日志浏览页面
app.get('/browse/:deviceId', (req, res) => {
    const { deviceId } = req.params;
    logAccess(req, `浏览设备日志 - 设备ID: ${deviceId}`);
    
    const deviceLogDir = getDeviceLogDir(deviceId);
    
    if (!fs.existsSync(deviceLogDir)) {
        return res.send(`
            <h1>设备 ${deviceId} 暂无日志</h1>
            <a href="/">返回主页</a>
        `);
    }
    
    try {
        const files = fs.readdirSync(deviceLogDir)
            .filter(file => file.endsWith('.log'))
            .map(file => {
                const filePath = path.join(deviceLogDir, file);
                const stats = fs.statSync(filePath);
                return {
                    name: file,
                    size: stats.size,
                    mtime: stats.mtime
                };
            })
            .sort((a, b) => b.mtime - a.mtime);
        
        let html = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>设备 ${deviceId} 日志列表</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
                th { background-color: #f2f2f2; }
                .btn { padding: 6px 12px; text-decoration: none; background: #007bff; color: white; border-radius: 3px; margin: 2px; }
                .btn:hover { background: #0056b3; }
                .file-size { text-align: right; }
            </style>
        </head>
        <body>
            <h1>设备 ${deviceId} 日志文件</h1>
            <a href="/" class="btn">← 返回主页</a>
            <a href="/logs/device_${deviceId}" class="btn">直接访问目录</a>
            
            <table>
                <thead>
                    <tr>
                        <th>文件名</th>
                        <th>大小</th>
                        <th>修改时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        files.forEach(file => {
            const sizeKB = (file.size / 1024).toFixed(2);
            const timeStr = new Date(file.mtime).toLocaleString('zh-CN');
            
            html += `
                    <tr>
                        <td>${file.name}</td>
                        <td class="file-size">${sizeKB} KB</td>
                        <td>${timeStr}</td>
                        <td>
                            <a href="/view/${deviceId}/${file.name}" class="btn">查看</a>
                            <a href="/logs/device_${deviceId}/${file.name}" class="btn" download>下载</a>
                        </td>
                    </tr>
            `;
        });
        
        html += `
                </tbody>
            </table>
        </body>
        </html>
        `;
        
        res.send(html);
        
    } catch (error) {
        res.status(500).send('读取目录失败: ' + error.message);
    }
});

// 查看日志文件内容
app.get('/view/:deviceId/:fileName', (req, res) => {
    const { deviceId, fileName } = req.params;
    logAccess(req, `查看日志文件 - 设备ID: ${deviceId}, 文件: ${fileName}`);
    
    const filePath = path.join(getDeviceLogDir(deviceId), fileName);
    
    if (!fs.existsSync(filePath)) {
        return res.status(404).send('文件不存在');
    }
    
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');
        
        let html = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>${fileName}</title>
            <style>
                body { font-family: 'Courier New', monospace; margin: 20px; background: #f8f9fa; }
                .header { background: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
                .log-content { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 5px; max-height: 600px; overflow-y: auto; }
                .line { margin: 2px 0; padding: 5px; border-radius: 3px; }
                .line:hover { background: #4a5568; }
                .line-number { color: #a0aec0; margin-right: 10px; }
                .btn { padding: 8px 16px; text-decoration: none; background: #007bff; color: white; border-radius: 3px; margin: 5px; }
                .search-box { margin: 10px 0; }
                .search-box input { padding: 8px; width: 300px; }
            </style>
            <script>
                function searchLog() {
                    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                    const lines = document.querySelectorAll('.line');
                    
                    lines.forEach(line => {
                        const text = line.textContent.toLowerCase();
                        if (searchTerm === '' || text.includes(searchTerm)) {
                            line.style.display = 'block';
                        } else {
                            line.style.display = 'none';
                        }
                    });
                }
            </script>
        </head>
        <body>
            <div class="header">
                <h1>📄 ${fileName}</h1>
                <a href="/browse/${deviceId}" class="btn">← 返回文件列表</a>
                <a href="/logs/device_${deviceId}/${fileName}" class="btn" download>下载文件</a>
                
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="搜索日志内容..." onkeyup="searchLog()">
                </div>
                
                <p><strong>文件大小:</strong> ${(fs.statSync(filePath).size / 1024).toFixed(2)} KB</p>
                <p><strong>总行数:</strong> ${lines.length}</p>
            </div>
            
            <div class="log-content">
        `;
        
        lines.forEach((line, index) => {
            const lineNumber = (index + 1).toString().padStart(4, '0');
            const escapedLine = line.replace(/</g, '&lt;').replace(/>/g, '&gt;');
            html += `<div class="line"><span class="line-number">${lineNumber}:</span>${escapedLine}</div>`;
        });
        
        html += `
            </div>
        </body>
        </html>
        `;
        
        res.send(html);
        
    } catch (error) {
        res.status(500).send('读取文件失败: ' + error.message);
    }
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`挖机日志服务器启动成功！`);
    console.log(`访问地址: http://localhost:${PORT}`);
    console.log(`日志存储目录: ${LOG_DIR}`);
    console.log(`API接口:`);
    console.log(`  检查上传: GET /getwjuplog.aspx?terminalid={设备ID}&pwd=653033`);
    console.log(`  上传日志: POST /logup?id={设备ID}&file={文件名}`);
});
