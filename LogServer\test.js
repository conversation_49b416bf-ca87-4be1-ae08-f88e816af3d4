const http = require('http');
const querystring = require('querystring');

const SERVER_HOST = 'localhost';
const SERVER_PORT = process.env.PORT || 8688;

// 测试数据
const TEST_DEVICE_ID = '19';
const TEST_PASSWORD = '653033';
const TEST_LOG_CONTENT = `2024-01-15 10:30:00 [INFO] 应用启动
2024-01-15 10:30:01 [INFO] 初始化完成
2024-01-15 10:30:02 [INFO] 开始工作循环
2024-01-15 10:30:03 [DEBUG] 连接数据库成功
2024-01-15 10:30:04 [INFO] 等待任务...
2024-01-15 10:30:05 [INFO] 收到新任务
2024-01-15 10:30:06 [INFO] 任务处理中...
2024-01-15 10:30:07 [INFO] 任务完成
2024-01-15 10:30:08 [ERROR] 网络连接异常
2024-01-15 10:30:09 [INFO] 重新连接成功`;

console.log('🧪 挖机日志服务器测试工具');
console.log('================================');

// 测试1: 检查日志上传状态
function testCheckUpload() {
    return new Promise((resolve, reject) => {
        const url = `/getwjuplog.aspx?terminalid=${TEST_DEVICE_ID}&pwd=${TEST_PASSWORD}`;
        
        console.log('\n📋 测试1: 检查日志上传状态');
        console.log(`请求: GET ${url}`);
        
        const req = http.request({
            hostname: SERVER_HOST,
            port: SERVER_PORT,
            path: url,
            method: 'GET'
        }, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`状态码: ${res.statusCode}`);
                console.log(`响应: ${data}`);
                
                try {
                    const result = JSON.parse(data);
                    if (result.Result === true) {
                        console.log('✅ 检查上传状态 - 成功');
                        resolve(true);
                    } else {
                        console.log('❌ 检查上传状态 - 失败:', result.Msg);
                        resolve(false);
                    }
                } catch (error) {
                    console.log('❌ 解析响应失败:', error.message);
                    reject(error);
                }
            });
        });
        
        req.on('error', (error) => {
            console.log('❌ 请求失败:', error.message);
            reject(error);
        });
        
        req.end();
    });
}

// 测试2: 上传日志文件
function testUploadLog() {
    return new Promise((resolve, reject) => {
        const fileName = `test_log_${Date.now()}`;
        const url = `/logup?id=${TEST_DEVICE_ID}&file=${fileName}`;
        
        console.log('\n📤 测试2: 上传日志文件');
        console.log(`请求: POST ${url}`);
        console.log(`文件名: ${fileName}`);
        console.log(`内容大小: ${Buffer.byteLength(TEST_LOG_CONTENT, 'utf8')} 字节`);
        
        const req = http.request({
            hostname: SERVER_HOST,
            port: SERVER_PORT,
            path: url,
            method: 'POST',
            headers: {
                'Content-Type': 'text/html; charset=utf-8',
                'Content-Length': Buffer.byteLength(TEST_LOG_CONTENT, 'utf8')
            }
        }, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`状态码: ${res.statusCode}`);
                console.log(`响应: ${data}`);
                
                if (res.statusCode === 200 && data.includes('成功')) {
                    console.log('✅ 上传日志文件 - 成功');
                    resolve(fileName);
                } else {
                    console.log('❌ 上传日志文件 - 失败');
                    resolve(null);
                }
            });
        });
        
        req.on('error', (error) => {
            console.log('❌ 请求失败:', error.message);
            reject(error);
        });
        
        req.write(TEST_LOG_CONTENT);
        req.end();
    });
}

// 测试3: 检查Web界面
function testWebInterface() {
    return new Promise((resolve, reject) => {
        console.log('\n🌐 测试3: 检查Web界面');
        console.log('请求: GET /');
        
        const req = http.request({
            hostname: SERVER_HOST,
            port: SERVER_PORT,
            path: '/',
            method: 'GET'
        }, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`状态码: ${res.statusCode}`);
                
                if (res.statusCode === 200 && data.includes('挖机日志管理系统')) {
                    console.log('✅ Web界面 - 正常');
                    resolve(true);
                } else {
                    console.log('❌ Web界面 - 异常');
                    resolve(false);
                }
            });
        });
        
        req.on('error', (error) => {
            console.log('❌ 请求失败:', error.message);
            reject(error);
        });
        
        req.end();
    });
}

// 运行所有测试
async function runTests() {
    try {
        console.log(`🔗 连接服务器: http://${SERVER_HOST}:${SERVER_PORT}`);
        
        // 等待服务器启动
        console.log('⏳ 等待服务器响应...');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 执行测试
        const checkResult = await testCheckUpload();
        const uploadResult = await testUploadLog();
        const webResult = await testWebInterface();
        
        // 测试结果汇总
        console.log('\n📊 测试结果汇总');
        console.log('================================');
        console.log(`检查上传状态: ${checkResult ? '✅ 通过' : '❌ 失败'}`);
        console.log(`上传日志文件: ${uploadResult ? '✅ 通过' : '❌ 失败'}`);
        console.log(`Web界面访问: ${webResult ? '✅ 通过' : '❌ 失败'}`);
        
        const passedTests = [checkResult, uploadResult, webResult].filter(Boolean).length;
        console.log(`\n总计: ${passedTests}/3 项测试通过`);
        
        if (passedTests === 3) {
            console.log('\n🎉 所有测试通过！服务器运行正常');
            console.log(`\n🌐 访问Web界面: http://${SERVER_HOST}:${SERVER_PORT}`);
        } else {
            console.log('\n⚠️  部分测试失败，请检查服务器配置');
        }
        
    } catch (error) {
        console.log('\n❌ 测试过程中发生错误:', error.message);
        console.log('\n💡 请确保服务器已启动并运行在端口', SERVER_PORT);
    }
}

// 启动测试
runTests();
