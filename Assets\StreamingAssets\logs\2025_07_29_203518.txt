20:35:18==========开始统计车数=======
UnityEngine.Debug:Log (object)
UI_Camera:ShowTotal () (at Assets/Script/UI/UI_Camera.cs:831)
UI_Camera:UI_Start () (at Assets/Script/UI/UI_Camera.cs:92)
UI_Manager:Show (string) (at Assets/Script/UI/UI_Manager.cs:51)
App:Start () (at Assets/Script/App.cs:60)

20:35:18当天未上传总共条数：0
UnityEngine.Debug:Log (object)
AppData:GetLocal (LocalXmlData,int&,int&) (at Assets/Script/AppData.cs:524)
AppData:GetAllLocal (int&,int&) (at Assets/Script/AppData.cs:490)
UI_Camera:ShowTotal () (at Assets/Script/UI/UI_Camera.cs:832)
UI_Camera:UI_Start () (at Assets/Script/UI/UI_Camera.cs:92)
UI_Manager:Show (string) (at Assets/Script/UI/UI_Manager.cs:51)
App:Start () (at Assets/Script/App.cs:60)

20:35:18[LogManager] 上传日志文件: D:/cq/wj/Assets/StreamingAssets/logs/2025_07_29_202856.txt
UnityEngine.Debug:Log (object)
LogManager/<UploadLogs>d__11:MoveNext () (at Assets/Script/LogManager.cs:204)
UnityEngine.MonoBehaviour:StartCoroutine (System.Collections.IEnumerator)
LogManager:Start () (at Assets/Script/LogManager.cs:95)

20:35:19[LogManager] 上传成功，响应: 上传成功
UnityEngine.Debug:Log (object)
LogManager/<PostLogFile>d__12:MoveNext () (at Assets/Script/LogManager.cs:239)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:35:19[LogManager] 上传日志文件: D:/cq/wj/Assets/StreamingAssets/logs/2025_07_29_202909.txt
UnityEngine.Debug:Log (object)
LogManager/<UploadLogs>d__11:MoveNext () (at Assets/Script/LogManager.cs:204)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:35:19==没权限==
UnityEngine.Debug:Log (object)
车队模式:<CanWork>b__8_0 (string) (at Assets/Script/UI/车队模式.cs:87)
Http/<GetAsync>d__5:MoveNext () (at Assets/Script/Http.cs:91)
UnityEngine.UnitySynchronizationContext:ExecuteTasks ()

20:35:19[LogManager] 上传成功，响应: 上传成功
UnityEngine.Debug:Log (object)
LogManager/<PostLogFile>d__12:MoveNext () (at Assets/Script/LogManager.cs:239)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:35:19[LogManager] 上传日志文件: D:/cq/wj/Assets/StreamingAssets/logs/2025_07_29_203128.txt
UnityEngine.Debug:Log (object)
LogManager/<UploadLogs>d__11:MoveNext () (at Assets/Script/LogManager.cs:204)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:35:19[md5]WJ-DROID-2-2025-07-29 20:35:19<->0cf8697631b1e782
UnityEngine.Debug:Log (object)
NetManager:GetMd5Hash (string) (at Assets/Script/NetManager.cs:634)
NetManager:RequestLogin () (at Assets/Script/NetManager.cs:189)
NetManager:Update () (at Assets/Script/NetManager.cs:85)
App:Update () (at Assets/Script/App.cs:84)

20:35:19[LogManager] 上传成功，响应: 上传成功
UnityEngine.Debug:Log (object)
LogManager/<PostLogFile>d__12:MoveNext () (at Assets/Script/LogManager.cs:239)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:35:19[LogManager] 日志上传完成
UnityEngine.Debug:Log (object)
LogManager/<UploadLogs>d__11:MoveNext () (at Assets/Script/LogManager.cs:214)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:35:19[md5]WJ-DROID-2-2025-07-29 20:35:19<->0cf8697631b1e782
UnityEngine.Debug:Log (object)
NetManager:GetMd5Hash (string) (at Assets/Script/NetManager.cs:634)
NetManager:RequestLogin () (at Assets/Script/NetManager.cs:189)
NetManager:Update () (at Assets/Script/NetManager.cs:85)
App:Update () (at Assets/Script/App.cs:84)

