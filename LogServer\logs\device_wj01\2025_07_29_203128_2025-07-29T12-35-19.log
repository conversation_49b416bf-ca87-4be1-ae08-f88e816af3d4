20:31:29==========开始统计车数=======
UnityEngine.Debug:Log (object)
UI_Camera:ShowTotal () (at Assets/Script/UI/UI_Camera.cs:831)
UI_Camera:UI_Start () (at Assets/Script/UI/UI_Camera.cs:92)
UI_Manager:Show (string) (at Assets/Script/UI/UI_Manager.cs:51)
App:Start () (at Assets/Script/App.cs:60)

20:31:29当天未上传总共条数：0
UnityEngine.Debug:Log (object)
AppData:GetLocal (LocalXmlData,int&,int&) (at Assets/Script/AppData.cs:524)
AppData:GetAllLocal (int&,int&) (at Assets/Script/AppData.cs:490)
UI_Camera:ShowTotal () (at Assets/Script/UI/UI_Camera.cs:832)
UI_Camera:UI_Start () (at Assets/Script/UI/UI_Camera.cs:92)
UI_Manager:Show (string) (at Assets/Script/UI/UI_Manager.cs:51)
App:Start () (at Assets/Script/App.cs:60)

20:31:29[LogManager] 上传日志文件: D:/cq/wj/Assets/StreamingAssets/logs/2025_07_29_202856.txt
UnityEngine.Debug:Log (object)
LogManager/<UploadLogs>d__11:MoveNext () (at Assets/Script/LogManager.cs:183)
UnityEngine.MonoBehaviour:StartCoroutine (System.Collections.IEnumerator)
LogManager:Start () (at Assets/Script/LogManager.cs:91)

20:31:29[LogManager] 上传成功，响应: 上传成功
UnityEngine.Debug:Log (object)
LogManager/<PostLogFile>d__12:MoveNext () (at Assets/Script/LogManager.cs:218)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:31:29[LogManager] 上传日志文件: D:/cq/wj/Assets/StreamingAssets/logs/2025_07_29_202856.txt.meta
UnityEngine.Debug:Log (object)
LogManager/<UploadLogs>d__11:MoveNext () (at Assets/Script/LogManager.cs:183)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:31:29==没权限==
UnityEngine.Debug:Log (object)
车队模式:<CanWork>b__8_0 (string) (at Assets/Script/UI/车队模式.cs:87)
Http/<GetAsync>d__5:MoveNext () (at Assets/Script/Http.cs:91)
UnityEngine.UnitySynchronizationContext:ExecuteTasks ()

20:31:29[LogManager] 上传成功，响应: 上传成功
UnityEngine.Debug:Log (object)
LogManager/<PostLogFile>d__12:MoveNext () (at Assets/Script/LogManager.cs:218)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:31:29[LogManager] 上传日志文件: D:/cq/wj/Assets/StreamingAssets/logs/2025_07_29_202909.txt
UnityEngine.Debug:Log (object)
LogManager/<UploadLogs>d__11:MoveNext () (at Assets/Script/LogManager.cs:183)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:31:29[md5]WJ-DROID-2-2025-07-29 20:31:29<->0cb35b0e359f0213
UnityEngine.Debug:Log (object)
NetManager:GetMd5Hash (string) (at Assets/Script/NetManager.cs:634)
NetManager:RequestLogin () (at Assets/Script/NetManager.cs:189)
NetManager:Update () (at Assets/Script/NetManager.cs:85)
App:Update () (at Assets/Script/App.cs:84)

20:31:29[LogManager] 上传成功，响应: 上传成功
UnityEngine.Debug:Log (object)
LogManager/<PostLogFile>d__12:MoveNext () (at Assets/Script/LogManager.cs:218)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:31:29[LogManager] 上传日志文件: D:/cq/wj/Assets/StreamingAssets/logs/2025_07_29_202909.txt.meta
UnityEngine.Debug:Log (object)
LogManager/<UploadLogs>d__11:MoveNext () (at Assets/Script/LogManager.cs:183)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:31:29[LogManager] 上传成功，响应: 上传成功
UnityEngine.Debug:Log (object)
LogManager/<PostLogFile>d__12:MoveNext () (at Assets/Script/LogManager.cs:218)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:31:29[LogManager] 日志上传完成
UnityEngine.Debug:Log (object)
LogManager/<UploadLogs>d__11:MoveNext () (at Assets/Script/LogManager.cs:193)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

20:31:30[md5]WJ-DROID-2-2025-07-29 20:31:30<->ed3adc2c4511ae04
UnityEngine.Debug:Log (object)
NetManager:GetMd5Hash (string) (at Assets/Script/NetManager.cs:634)
NetManager:RequestLogin () (at Assets/Script/NetManager.cs:189)
NetManager:Update () (at Assets/Script/NetManager.cs:85)
App:Update () (at Assets/Script/App.cs:84)

20:32:00Assets\Script\AppData.cs(2016,14): warning CS0219: The variable 'find' is assigned but its value is never used

20:32:00Assets\Script\AppData.cs(2050,14): warning CS0219: The variable 'find' is assigned but its value is never used

20:32:00Assets\GUIConsole\GUIConsole.cs(350,26): warning CS0618: 'WWW' is obsolete: 'Use UnityWebRequest, a fully featured replacement which is more efficient and has additional features'

20:32:00Assets\GUIConsole\GUIConsole.cs(356,13): warning CS0618: 'TextEditor.content' is obsolete: 'Please use 'text' instead of 'content''

20:32:00Assets\Script\Http.cs(28,26): warning CS0168: The variable 'ex' is declared but never used

20:32:00Assets\Script\UI\Set\UI_Set_Work.cs(114,48): warning CS0168: The variable 'SleepTimeStr' is declared but never used

20:32:00Assets\Script\UI\Set\UI_Set_Work.cs(115,30): warning CS0168: The variable 'SleepTime' is declared but never used

20:32:00Assets\Script\NetManager.cs(456,18): warning CS0219: The variable 'state' is assigned but its value is never used

20:32:00Assets\Script\NetManager.cs(575,26): warning CS0168: The variable 'ex' is declared but never used

20:32:00Assets\Script\Http.cs(199,26): warning CS0168: The variable 'ex' is declared but never used

20:32:00Assets\Script\AppData.cs(1930,27): warning CS0067: The event 'AppData.GoodsChangeEvent' is never used

20:32:00Assets\Script\App.cs(23,19): warning CS0414: The field 'App.NetWorkStartTime' is assigned but its value is never used

